<!DOCTYPE html><!--  Last Published: Tue Sep 09 2025 08:51:55 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="689ba69a761f6553030a36ce" data-wf-site="68966e7aa6ced226071c8ce6">

<head>
  <meta charset="utf-8">
  <title>Not Found</title>
  <meta content="Not Found" property="og:title">
  <meta content="Not Found" property="twitter:title">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/components.css" rel="stylesheet" type="text/css">
  <link href="css/ashnu.css" rel="stylesheet" type="text/css">
  <script
    type="text/javascript">!function (o, c) { var n = c.documentElement, t = " w-mod-"; n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch") }(window, document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
</head>

<body class="body-404">
  <div class="custom-code">
    <div class="w-embed custom-code">
      <style>
        /* General */
        html {
          font-size: 14.4px;
        }

        body {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        a {
          color: inherit;
        }

        @media only screen and (max-width: 767px) {
          a {
            cursor: default !important;
          }
        }

        * {
          -webkit-tap-highlight-color: transparent;
        }

        *:focus {
          outline: 0 !important;
        }

        input {
          -webkit-appearance: none;
          -webkit-border-radius: 0;
        }

        textarea {
          border: none;
          overflow: auto;
          outline: none;
          -webkit-box-shadow: none;
          -moz-box-shadow: none;
          box-shadow: none;
          resize: none;
          /*remove the resize handle on the bottom right*/
        }

        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
          -webkit-transition: "color 9999s ease-out, background-color 9999s ease-out";
          -webkit-transition-delay: 9999s;
        }

        .input-field:hover::placeholder {
          opacity: 0.5;
        }

        .input-field:focus::placeholder {
          opacity: 0.5;
        }

        /* Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        /* Firefox */
        input[type=number] {
          -moz-appearance: textfield;
        }

        .disable-slider-button {
          pointer-events: none;
          cursor: auto !important;
          opacity: 0.3;
        }

        /* END General */
      </style>
      <style>
        video::-webkit-media-controls {
          display: none;
          -webkit-appearance: none;
        }

        *::-webkit-media-controls-start-playback-button {
          display: none !important;
          -webkit-appearance: none;
        }

        video::-webkit-media-controls-overlay-play-button {
          display: none !important;
        }

        a {
          text-decoration-thickness: 1px !important;
          text-underline-offset: 3px;
        }

        @media screen and (min-width: 991px) {
          .container.is--header.is--scrolled {
            background: #ffffff;
          }
        }

        .header.is--open {
          background: #ffffff;
        }

        @media screen and (max-width: 991px) {
          .container.is--header.is--scrolled .header {
            background: #ffffff;
          }
        }

        .header__menu-item:first-child {
          border-top: none;
        }

        .header.is--open .header__menu-burger-icon-line.is--1 {
          top: 50%;
          opacity: 0;
        }

        .header.is--open .header__menu-burger-icon-line.is--3 {
          bottom: 50%;
          opacity: 0;
        }

        .footer__text a,
        .footer__accuraten a,
        .policy__rich a {
          text-decoration: underline;
        }

        a.footer__text,
        .footer__text a,
        .footer__accuraten a,
        .policy__rich a {
          transition: opacity 0.3s ease-in-out;
        }

        .footer a:hover,
        .policy__rich a:hover {
          opacity: 0.6;
        }

        .footer-wrap:not(.is--home) .footer__animation {
          display: none;
        }

        .content-item__card-details {
          visibility: hidden;
          transition-behavior: allow-discrete;
        }

        .content-item__card-content.is--open .content-item__card-details {
          opacity: 1;
          visibility: visible;
        }

        .content-item__card-content.is--open .content-item__card-label {
          opacity: 0;
        }

        .content-item__card-content.is--open .content-item-toggle-icon-line.is--1 {
          transform: rotateZ(0deg);
        }

        .content-item__card-open-text a {
          color: #fff;
          transition: opacity 0.3s ease-in-out;
        }

        .content-item__card-open-text a:hover {
          opacity: 0.6;
        }

        .content-item__card-content-list::marker {
          content: "•  ";
          font-size: 1.2em;
          margin-right: 24px;
        }

        .content-item__card {
          transition: 0.3s filter ease-in-out;
        }

        html:not(.wf-design-mode) .content-item__card {
          filter: blur(10px);
        }

        .content-item__card.is--visible {
          filter: blur(0px) !important;
        }

        .container:has(.policy__rich) {
          max-width: 1136px;
        }

        .policy__rich h2 {
          font-weight: 500;
          font-size: 3.06rem;
        }

        @media screen and (max-width: 767px) {
          .policy__rich h2 {
            font-size: 24px;
          }
        }

        a.footer__text {
          position: relative;
        }

        a.footer__text::before {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          width: 100%;
          height: 1px;
          background: currentColor;
          transform: scaleX(0);
          transform-origin: center left;
          transition: transform 0.3s ease-in-out;
        }

        a.footer__text:hover::before {
          transform: scaleX(1);
        }
      </style>
    </div>
  </div>
  <section class="section is--header">
    <div class="container is--header">
      <div class="header">
        <div class="w-layout-vflex header__top">
          <a href="index.html" class="w-inline-block">
            <div class="w-embed header__logo-wrap"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                fill="none" viewbox="0 0 236 32">
                <path fill="#000"
                  d="M4.293 27.995c2.134 2.065 4.29 3.524 5.211 2.537.854-.898-.314-2.807-1.91-5.03 5.482 4.648 10.064 7.567 11.48 6.13 1.527-1.572-1.865-6.264-6.223-11.405 5.571 5.118 12.557 9.81 14.87 7.476 2.337-2.335-2.403-9.026-7.57-15.02 5.796 5.007 9.997 7.88 11.48 6.444 1.437-1.437-1.37-5.928-6.268-11.81 1.932 1.64 4.29 3.143 5.19 2.223.875-.92-.54-3.323-2.854-5.523l-.022-.023c-2.09-2.065-4.268-3.547-5.212-2.559-.853.898.697 3.233 1.977 4.984C18.894 1.884 14.312-1.057 12.92.357 11.414 1.93 14.806 6.51 19.23 11.65 13.593 6.531 6.584 1.951 4.27 4.286s2.448 8.846 7.547 14.908c-5.75-5.052-9.951-7.813-11.434-6.331-1.46 1.436 1.37 5.882 6.312 11.742-1.976-1.595-4.313-3.099-5.211-2.178-.899.943.517 3.345 2.763 5.545l.045.023ZM163.78 31.34h-3.774v-4.558c-6.334-.314-9.906-3.816-10.131-9.16.225-5.455 3.954-8.958 10.131-9.137V3.927h3.774v4.558c6.267.18 10.108 3.682 10.108 9.137 0 5.344-4.021 8.846-10.108 9.16v4.558Zm69.366-22.676v14.75H236v7.926h-3.594v-4.715h-13.388v4.715h-3.594v-7.925h1.774c1.438-.898 2.044-2.268 2.044-7.454V8.664h13.905ZM123.752 19.15h-3.011v7.476h-3.773V8.665h3.773v7.34h3.055c.674-4.49 4.021-7.543 8.873-7.543 5.212 0 8.761 3.862 8.761 9.273 0 5.455-3.549 9.115-8.761 9.115-4.964 0-8.356-3.031-8.917-7.701Zm-63.976-7.274-.022-3.21h3.774v17.96h-3.774l.022-3.233c-1.1 1.886-3.37 3.458-6.334 3.458-4.875 0-8.424-3.435-8.424-9.228 0-5.478 3.572-9.182 8.491-9.182 2.853 0 5.324 1.571 6.267 3.435Zm116.472 5.86c0-5.411 3.572-9.273 9.255-9.273 5.436 0 8.985 3.862 8.985 9.273 0 5.455-3.549 9.115-8.985 9.115-5.683 0-9.255-3.66-9.255-9.116Zm-83.765 8.89H67.549V8.665h3.773V23.37h6.807V8.664h3.774V23.37h6.806V8.664h3.774v17.961Zm16.667-10.777V8.665h3.774v17.961h-3.774v-7.521h-8.872v7.521h-3.774V8.665h3.774v7.184h8.872Zm100.658 0V8.665h3.774v17.961h-3.774v-7.521h-8.873v7.521h-3.774V8.665h3.774v7.184h8.873Zm-19.161 1.842c0-3.615-2.022-6.152-5.144-6.152-3.257 0-5.414 2.537-5.414 6.152 0 3.614 2.157 6.084 5.414 6.084 3.122 0 5.144-2.47 5.144-6.084Zm-53.058 0c0-3.615-2.022-6.152-4.92-6.152-3.032 0-5.189 2.56-5.189 6.152 0 3.614 2.157 6.084 5.189 6.084 2.898 0 4.92-2.47 4.92-6.084Zm22.418 6.061V11.515c-3.796.158-6.29 2.38-6.29 6.107 0 3.503 2.494 5.972 6.29 6.13Zm3.774-12.236v12.236c4.021-.18 6.267-2.626 6.267-6.129 0-3.727-2.246-5.95-6.267-6.107Zm-104.027 6.22c0-3.57-2.157-6.04-5.436-6.04-3.325 0-5.459 2.447-5.459 5.927 0 3.57 2.134 6.017 5.459 6.017 3.28 0 5.436-2.447 5.436-5.905Zm161.016 5.68h8.603V11.852h-6.492v4.58c0 3.615-.427 6.085-2.111 6.983Z">
                </path>
              </svg></div>
          </a><button class="header__mobile-menu-button"><span class="header__menu-burger-icon-line is--1"></span><span
              class="header__menu-burger-icon-line is--2"></span><span
              class="header__menu-burger-icon-line is--3"></span></button>
        </div>
        <nav id="w-node-_5d9de279-3a53-a598-cc2c-81e1c30bd20b-c30bd202" class="header__menu">
          <ul role="list" class="header__menu-wrapper">
            <li class="header__menu-item">
              <a href="/#focus" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Фокус инвестиций</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#about" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">O Фонде</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#projects" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Проекты</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#submit-project" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Предложить проект</div>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </section>
  <div class="section">
    <div class="container">
      <div class="page-404">
        <h1 class="page-404__title">404</h1>
        <a href="index.html" class="text-sm">На главную</a>
      </div>
    </div>
  </div>
  <div class="section is--footer">
    <footer class="footer">
      <div class="container">
        <div class="footer__wrap">
          <div class="footer__cell">
            <div class="w-embed footer__logo"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                fill="none" viewbox="0 0 92 92">
                <path fill="#000"
                  d="M12.34 80.485c6.133 5.938 12.332 10.133 14.979 7.293 2.454-2.582-.904-8.068-5.488-14.458 15.754 13.36 28.926 21.752 32.994 17.62 4.39-4.518-5.36-18.008-17.885-32.79 16.013 14.717 36.093 28.207 42.743 21.494 6.715-6.712-6.908-25.947-21.759-43.181C74.583 50.858 86.657 59.12 90.918 54.989c4.133-4.131-3.938-17.04-18.014-33.952 5.553 4.712 12.332 9.037 14.915 6.39 2.518-2.646-1.55-9.553-8.2-15.878l-.065-.065C73.55 5.546 67.287 1.286 64.574 4.126c-2.453 2.581 2.002 9.294 5.683 14.329C54.308 5.417 41.137-3.039 37.133 1.027c-4.326 4.519 5.424 17.686 18.144 32.468C39.07 18.778 18.925 5.61 12.274 12.323c-6.65 6.713 7.038 25.432 21.695 42.86C17.44 40.658 5.366 32.72 1.104 36.98c-4.197 4.13 3.939 16.911 18.144 33.758-5.682-4.583-12.397-8.908-14.98-6.261-2.583 2.71 1.485 9.617 7.942 15.943l.13.065Z">
                </path>
              </svg></div>
            <div class="text-xs footer__accuraten">Designed by <a href="https://accuraten.com/"
                target="_blank">Accuraten</a>
            </div>
          </div>
          <div class="footer__cell">
            <a href="mailto:<EMAIL>" class="text-xs footer__text is--email"><EMAIL></a>
            <div class="footer__policy-links">
              <a href="politika.html" class="text-sm footer__text">Политика конфиденциальности</a>
              <a href="soglashenie.html" class="text-sm footer__text">Пользовательское соглашение</a>
            </div>
            <div class="text-xs footer__text is--copyright">@2025. ООО Ашню</div>
            <div class="text-xs footer__text is--legal">Для направления запросов необходимо дать согласие на обработку
              <a href="soglasie.html">персональных данных</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
    <div class="footer__animation"><video src="https://ashnu.ru/ashnu-footer-stream.mp4" playsinline="" muted="" loop=""
        preload="none" poster="https://ashnu.ru/ashnu-footer-poster.png" class="footer__video"></video><img
        src="images/footer-mobile.png" loading="eager" sizes="100vw"
        srcset="images/footer-mobile-p-500.png 500w, images/footer-mobile-p-800.png 800w, images/footer-mobile.png 1024w"
        alt="" class="footer__mobile-img"></div>
  </div>
  <script src="/js/jquery-3.5.1.min.js" type="text/javascript"></script>
  <script src="js/ashnu.js" type="text/javascript"></script>
  <script fetchpriority="high" type="text/javascript" src="/js/gsap.min.js"></script>
  <script fetchpriority="high" type="text/javascript" src="/js/ScrollTrigger.min.js"></script>
  <script>
    //#region [Header]
    const heroEl = document.querySelector('.section.is--hero')
    const headerEl = document.querySelector('.container.is--header')
    // Hide menu after hero and reveal back on scroll up
    const actionNav = gsap.to(headerEl, { y: '-100%', duration: 0.3, ease: 'power2.inOut', paused: true });
    // Change menu after hero
    new ScrollTrigger({
      trigger: heroEl,
      start: 'top top',
      end: 'bottom top',
      onLeave: () => {
        headerEl.classList.add('is--scrolled');
        actionNav.play();
      },
      onEnterBack: () => {
        headerEl.classList.remove('is--scrolled');
        actionNav.reverse();
      },
    })
    new ScrollTrigger({
      trigger: document.body,
      start: 'top top',
      end: 'bottom top',
      onUpdate: ({ progress, direction }) => {
        if (direction === 1 && !ScrollTrigger.isInViewport(heroEl)) {
          actionNav.play();
        } else if (direction === -1) {
          actionNav.reverse();
        }
      }
    })
    //#endregion [Header]
    //#region [Mobile menu]
    const mobileHeader = document.querySelector('.header');
    const mobileMenuTriggerButton = document.querySelector('.header__mobile-menu-button');
    const headerLinks = document.querySelectorAll('.header__menu-link');
    if (mobileHeader && mobileMenuTriggerButton) {
      mobileMenuTriggerButton.addEventListener('click', () => {
        mobileHeader.classList.toggle('is--open');
      });
      headerLinks.forEach((link) => {
        link.addEventListener('click', () => {
          mobileHeader.classList.remove('is--open');
        })
      })
    }
    //#endregion [Mobile menu]
  </script>
</body>

</html>