<!DOCTYPE html><!--  Last Published: Tue Sep 09 2025 08:51:55 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="68966e7aa6ced226071c8cec" data-wf-site="68966e7aa6ced226071c8ce6">

<head>
  <meta charset="utf-8">
  <title>Ашню - Независимый венчурный фонд</title>
  <meta
    content="От уникальных идей к большим компаниям. Мы даем возможность командам разрабатывать технологичные решения и становиться лидерами рынка."
    name="description">
  <meta content="Ашню - Независимый венчурный фонд" property="og:title">
  <meta
    content="От уникальных идей к большим компаниям. Мы даем возможность командам разрабатывать технологичные решения и становиться лидерами рынка."
    property="og:description">
  <meta content="/images/og.png" property="og:image">
  <meta content="Ашню - Независимый венчурный фонд" property="twitter:title">
  <meta
    content="От уникальных идей к большим компаниям. Мы даем возможность командам разрабатывать технологичные решения и становиться лидерами рынка."
    property="twitter:description">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/components.css" rel="stylesheet" type="text/css">
  <link href="css/ashnu.css" rel="stylesheet" type="text/css">
  <link href="css/custom.css" rel="stylesheet" type="text/css">
  <script
    type="text/javascript">!function (o, c) { var n = c.documentElement, t = " w-mod-"; n.className += t + "js", ("ontouchstart" in o || o.DocumentTouch && c instanceof DocumentTouch) && (n.className += t + "touch") }(window, document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" as="image" href="https://ashnu.ru/ashnu-header-desktop-poster-2.png" crossorigin="anonymous"
    fetchpriority="high">
  <link rel="stylesheet" fetchpriority="high" href="/js/swiper-bundle.min.css">
</head>

<body>
  <div class="custom-code">
    <div class="w-embed custom-code">
      <style>
        /* General */
        html {
          font-size: 14.4px;
        }

        body {
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        a {
          color: inherit;
        }

        @media only screen and (max-width: 767px) {
          a {
            cursor: default !important;
          }
        }

        * {
          -webkit-tap-highlight-color: transparent;
        }

        *:focus {
          outline: 0 !important;
        }

        input {
          -webkit-appearance: none;
          -webkit-border-radius: 0;
        }

        textarea {
          border: none;
          overflow: auto;
          outline: none;
          -webkit-box-shadow: none;
          -moz-box-shadow: none;
          box-shadow: none;
          resize: none;
          /*remove the resize handle on the bottom right*/
        }

        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        input:-webkit-autofill:active {
          transition: background-color 5000000s ease-in-out 0s;
        }

        .input-field:hover::placeholder {
          opacity: 0.5;
        }

        .input-field:focus::placeholder {
          opacity: 0.5;
        }

        /* Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }

        /* Firefox */
        input[type=number] {
          -moz-appearance: textfield;
        }

        .disable-slider-button {
          pointer-events: none;
          cursor: auto !important;
          opacity: 0.3;
        }

        /* END General */
      </style>
      <style>
        video::-webkit-media-controls {
          display: none;
          -webkit-appearance: none;
        }

        *::-webkit-media-controls-start-playback-button {
          display: none !important;
          -webkit-appearance: none;
        }

        video::-webkit-media-controls-overlay-play-button {
          display: none !important;
        }

        a {
          text-decoration-thickness: 1px !important;
          text-underline-offset: 3px;
        }

        @media screen and (min-width: 991px) {
          .container.is--header.is--scrolled {
            background: #ffffff;
          }
        }

        .header.is--open {
          background: #ffffff;
        }

        @media screen and (max-width: 991px) {
          .container.is--header.is--scrolled .header {
            background: #ffffff;
          }
        }

        .header__menu-item:first-child {
          border-top: none;
        }

        .header.is--open .header__menu-burger-icon-line.is--1 {
          top: 50%;
          opacity: 0;
        }

        .header.is--open .header__menu-burger-icon-line.is--3 {
          bottom: 50%;
          opacity: 0;
        }

        .footer__text a,
        .footer__accuraten a,
        .policy__rich a {
          text-decoration: underline;
        }

        a.footer__text,
        .footer__text a,
        .footer__accuraten a,
        .policy__rich a {
          transition: opacity 0.3s ease-in-out;
        }

        .footer a:hover,
        .policy__rich a:hover {
          opacity: 0.6;
        }

        .footer-wrap:not(.is--home) .footer__animation {
          display: none;
        }

        .content-item__card-details {
          visibility: hidden;
          transition-behavior: allow-discrete;
        }

        .content-item__card-content.is--open .content-item__card-details {
          opacity: 1;
          visibility: visible;
        }

        .content-item__card-content.is--open .content-item__card-label {
          opacity: 0;
        }

        .content-item__card-content.is--open .content-item-toggle-icon-line.is--1 {
          transform: rotateZ(0deg);
        }

        .content-item__card-open-text a {
          color: #fff;
          transition: opacity 0.3s ease-in-out;
        }

        .content-item__card-open-text a:hover {
          opacity: 0.6;
        }

        .content-item__card-content-list::marker {
          content: "•  ";
          font-size: 1.2em;
          margin-right: 24px;
        }

        .content-item__card {
          transition: 0.3s filter ease-in-out;
        }

        html:not(.wf-design-mode) .content-item__card {
          filter: blur(10px);
        }

        .content-item__card.is--visible {
          filter: blur(0px) !important;
        }

        .container:has(.policy__rich) {
          max-width: 1136px;
        }

        .policy__rich h2 {
          font-weight: 500;
          font-size: 3.06rem;
        }

        @media screen and (max-width: 767px) {
          .policy__rich h2 {
            font-size: 24px;
          }
        }

        a.footer__text {
          position: relative;
        }

        a.footer__text::before {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          width: 100%;
          height: 1px;
          background: currentColor;
          transform: scaleX(0);
          transform-origin: center left;
          transition: transform 0.3s ease-in-out;
        }

        a.footer__text:hover::before {
          transform: scaleX(1);
        }
      </style>
    </div>
  </div>
  <section class="section is--header">
    <div class="container is--header">
      <div class="header">
        <div class="w-layout-vflex header__top">
          <a href="index.html" aria-current="page" class="w-inline-block w--current">
            <div class="w-embed header__logo-wrap"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                fill="none" viewbox="0 0 236 32">
                <path fill="#000"
                  d="M4.293 27.995c2.134 2.065 4.29 3.524 5.211 2.537.854-.898-.314-2.807-1.91-5.03 5.482 4.648 10.064 7.567 11.48 6.13 1.527-1.572-1.865-6.264-6.223-11.405 5.571 5.118 12.557 9.81 14.87 7.476 2.337-2.335-2.403-9.026-7.57-15.02 5.796 5.007 9.997 7.88 11.48 6.444 1.437-1.437-1.37-5.928-6.268-11.81 1.932 1.64 4.29 3.143 5.19 2.223.875-.92-.54-3.323-2.854-5.523l-.022-.023c-2.09-2.065-4.268-3.547-5.212-2.559-.853.898.697 3.233 1.977 4.984C18.894 1.884 14.312-1.057 12.92.357 11.414 1.93 14.806 6.51 19.23 11.65 13.593 6.531 6.584 1.951 4.27 4.286s2.448 8.846 7.547 14.908c-5.75-5.052-9.951-7.813-11.434-6.331-1.46 1.436 1.37 5.882 6.312 11.742-1.976-1.595-4.313-3.099-5.211-2.178-.899.943.517 3.345 2.763 5.545l.045.023ZM163.78 31.34h-3.774v-4.558c-6.334-.314-9.906-3.816-10.131-9.16.225-5.455 3.954-8.958 10.131-9.137V3.927h3.774v4.558c6.267.18 10.108 3.682 10.108 9.137 0 5.344-4.021 8.846-10.108 9.16v4.558Zm69.366-22.676v14.75H236v7.926h-3.594v-4.715h-13.388v4.715h-3.594v-7.925h1.774c1.438-.898 2.044-2.268 2.044-7.454V8.664h13.905ZM123.752 19.15h-3.011v7.476h-3.773V8.665h3.773v7.34h3.055c.674-4.49 4.021-7.543 8.873-7.543 5.212 0 8.761 3.862 8.761 9.273 0 5.455-3.549 9.115-8.761 9.115-4.964 0-8.356-3.031-8.917-7.701Zm-63.976-7.274-.022-3.21h3.774v17.96h-3.774l.022-3.233c-1.1 1.886-3.37 3.458-6.334 3.458-4.875 0-8.424-3.435-8.424-9.228 0-5.478 3.572-9.182 8.491-9.182 2.853 0 5.324 1.571 6.267 3.435Zm116.472 5.86c0-5.411 3.572-9.273 9.255-9.273 5.436 0 8.985 3.862 8.985 9.273 0 5.455-3.549 9.115-8.985 9.115-5.683 0-9.255-3.66-9.255-9.116Zm-83.765 8.89H67.549V8.665h3.773V23.37h6.807V8.664h3.774V23.37h6.806V8.664h3.774v17.961Zm16.667-10.777V8.665h3.774v17.961h-3.774v-7.521h-8.872v7.521h-3.774V8.665h3.774v7.184h8.872Zm100.658 0V8.665h3.774v17.961h-3.774v-7.521h-8.873v7.521h-3.774V8.665h3.774v7.184h8.873Zm-19.161 1.842c0-3.615-2.022-6.152-5.144-6.152-3.257 0-5.414 2.537-5.414 6.152 0 3.614 2.157 6.084 5.414 6.084 3.122 0 5.144-2.47 5.144-6.084Zm-53.058 0c0-3.615-2.022-6.152-4.92-6.152-3.032 0-5.189 2.56-5.189 6.152 0 3.614 2.157 6.084 5.189 6.084 2.898 0 4.92-2.47 4.92-6.084Zm22.418 6.061V11.515c-3.796.158-6.29 2.38-6.29 6.107 0 3.503 2.494 5.972 6.29 6.13Zm3.774-12.236v12.236c4.021-.18 6.267-2.626 6.267-6.129 0-3.727-2.246-5.95-6.267-6.107Zm-104.027 6.22c0-3.57-2.157-6.04-5.436-6.04-3.325 0-5.459 2.447-5.459 5.927 0 3.57 2.134 6.017 5.459 6.017 3.28 0 5.436-2.447 5.436-5.905Zm161.016 5.68h8.603V11.852h-6.492v4.58c0 3.615-.427 6.085-2.111 6.983Z">
                </path>
              </svg></div>
          </a><button class="header__mobile-menu-button"><span class="header__menu-burger-icon-line is--1"></span><span
              class="header__menu-burger-icon-line is--2"></span><span
              class="header__menu-burger-icon-line is--3"></span></button>
        </div>
        <nav id="w-node-_5d9de279-3a53-a598-cc2c-81e1c30bd20b-c30bd202" class="header__menu">
          <ul role="list" class="header__menu-wrapper">
            <li class="header__menu-item">
              <a href="/#focus" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Фокус инвестиций</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#about" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">O Фонде</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#projects" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Проекты</div>
              </a>
            </li>
            <li class="header__menu-item">
              <a href="/#submit-project" class="w-inline-block header__menu-link">
                <div class="text-sm header__menu-link-label">Предложить проект</div>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </section>
  <div id="hero" class="overlap-wrapper">
    <div class="overlap-sticky">
      <section class="section is--hero">
        <div class="container is--hero">
          <div class="hero">
            <h1 class="text-xl">От уникальных идей к большим компаниям</h1>
            <p class="text-base">Ашню - независимый венчурный фонд. Мы даем возможность командам разрабатывать
              технологичные решения и становиться лидерами рынка.</p>
          </div>
        </div>
        <div class="hero__animation"><video src="https://ashnu.ru/ashnu-header-desktop-stream.mp4" playsinline=""
            muted="" loop="" poster="https://ashnu.ru/ashnu-header-desktop-poster-2.png" preload="none"
            class="hero__desktop-video"></video><video src="https://ashnu.ru/ashnu-header-stream.mp4" playsinline=""
            muted="" loop="" poster="https://ashnu.ru/ashnu-header-poster.jpg" preload="none"
            class="hero__mobile-video"></video></div>
      </section>
    </div>
  </div>
  <div class="is--bottom overlap-wrapper">
    <div id="focus" class="section-anchor is--focus"></div>
    <div class="is--bottom overlap-wrapper"></div>
    <div class="is--bottom overlap-sticky">
      <section class="is--relative section">
        <div class="container">
          <div class="focus">
            <div class="section-header">
              <h2 class="text-xl section-header__title">Фокус инвестиций</h2>
              <div class="text-base section-header__description">Инвестируем в стартапы с потенциалом роста, за которыми
                стоят сильные команды и устойчивая стратегия развития продукта.</div>
            </div>
            <div class="focus__cards-wrapper swiper">
              <div class="is--focus-grid container swiper-wrapper"><button id=""
                  class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Искусственный интеллект и ML</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Опережайте отрасль с передовыми AI-разработками, используя экспертизу и ресурсы <a
                            href="https://iai.mipt.ru/" target="_blank">Института ИИ МФТИ</a> — одного из сильнейших
                          центров в стране.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/3.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/3-p-500.jpg 500w, images/3-p-800.jpg 800w, images/3.jpg 880w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Моделирование и цифровые двойники</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Развивайте ваши решения в моделировании и цифровых двойниках за счёт сотрудничества
                          с МФТИ-объединяя физику процессов, экономику производства и индивидуальные особенности
                          продукта.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/2.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/2-p-500.jpg 500w, images/2-p-800.jpg 800w, images/2.jpg 880w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Робототехника и автономный транспорт</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Расширьте потенциал ваших решений благодаря научной базе и экспертизе МФТИ — от автономных
                          систем до беспилотных платформ.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/3-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/3-min-p-500.jpg 500w, images/3-min-p-800.jpg 800w, images/3-min-p-1080.jpg 1080w, images/3-min.jpg 1320w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Телеком и технологии связи</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Усильте ваши телеком-решения за счёт экспертизы МФТИ-от AI-алгоритмов для 5G и сетей до
                          цифровых двойников.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/4-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/4-min-p-500.jpg 500w, images/4-min-p-800.jpg 800w, images/4-min-p-1080.jpg 1080w, images/4-min.jpg 1320w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Фотоника</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Расширьте возможности ваших фотонных решений, объединив их с экспертизой в области
                          инфракрасной спектроскопии, квантовой оптики и фотонных сенсоров.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/8-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/8-min-p-500.jpg 500w, images/8-min-p-800.jpg 800w, images/8-min-p-1080.jpg 1080w, images/8-min.jpg 1320w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Биотехнологии и медицина</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Ускоряйте создание лекарств: цифровые медицинские решения, разработка неинвазивных методов
                          диагностики и проектирование интеллектуальных систем для клинической практики и фармацевтики.
                        </p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/7-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/7-min-p-500.jpg 500w, images/7-min-p-800.jpg 800w, images/7-min-p-1080.jpg 1080w, images/7-min.jpg 1320w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Энергетика и аккумуляторы</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Усиливайте свои инновационные решения для хранения и генерации энергии, включая аккумуляторы,
                          водородные технологии и топливные элементы, в сотрудничестве с лидерами индустрии.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/6-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/6-min-p-500.jpg 500w, images/6-min-p-800.jpg 800w, images/6-min-p-1080.jpg 1080w, images/6-min.jpg 1320w"
                    class="content-item__card-img">
                </button><button id="" class="content-item__card swiper-slide is--focus">
                  <div class="content-item__card-content">
                    <div class="content-item__card-label">Оптимизация</div>
                    <div class="content-item__card-details">
                      <div class="w-richtext text-sm content-item__card-open-text">
                        <p>Повышайте эффективность продуктов за счёт ускорения алгоритмов, улучшения процессов,
                          маршрутов, а также использования передовых инструментов и методов.</p>
                      </div>
                    </div>
                    <div class="content-item__card-toggle-icon">
                      <div class="content-item-toggle-icon-line is--1"></div>
                      <div class="content-item-toggle-icon-line"></div>
                    </div>
                  </div><img src="images/5-min.jpg" loading="lazy" fetchpriority="high" alt=""
                    sizes="(max-width: 767px) 98vw, (max-width: 1333px) 99vw, 1320px"
                    srcset="images/5-min-p-500.jpg 500w, images/5-min-p-800.jpg 800w, images/5-min-p-1080.jpg 1080w, images/5-min.jpg 1320w"
                    class="content-item__card-img">
                </button>
                <div class="content-item__card swiper-slide is--other">
                  <div class="text-sm content-item__card-content is--other">
                    <div>Мы гибко подходим к выбору проектов и готовы рассмотреть иные отрасли.</div>
                    <ul role="list" class="focus__card-content-list">
                      <li class="focus__card-content-list-item">Аэрокосмические технологии</li>
                      <li class="focus__card-content-list-item">Живые системы</li>
                      <li class="focus__card-content-list-item">Физическое моделирование</li>
                      <li class="focus__card-content-list-item">АСУТП</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div class="focus__description">
              <h3 class="focus__description-text">Инвестируем в компании с сильной бизнес-моделью, коммерческим
                потенциалом и масштабируемыми решениями.</h3>
              <ul role="list" class="focus__description-list">
                <li class="focus__description-item">
                  <div class="focus__description-item-mask"><video src="https://ashnu.ru/ashnu-circle-1-stream.mp4"
                      playsinline="" muted="" loop="" preload="none" class="focus__circle-video"></video><img
                      src="images/mobile-circle-1.png" loading="lazy" fetchpriority="high" alt=""
                      class="focus__circle-img"></div>
                  <div class="text-base focus__description-item-text">Стадии инвестирования от seed до А</div>
                </li>
                <li class="focus__description-item">
                  <div class="focus__description-item-mask"><video src="https://ashnu.ru/ashnu-circle-2-stream.mp4"
                      playsinline="" muted="" loop="" preload="none" class="focus__circle-video is--2"></video><img
                      src="images/mobile-circle-2.png" loading="lazy" fetchpriority="high" alt=""
                      class="focus__circle-img"></div>
                  <div class="text-base focus__description-item-text">Компании с большим потенциалом роста</div>
                </li>
                <li class="focus__description-item">
                  <div class="focus__description-item-mask"><video src="https://ashnu.ru/ashnu-circle-3-stream.mp4"
                      playsinline="" muted="" loop="" preload="none" class="focus__circle-video is--3"></video><img
                      src="images/mobile-circle-3.png" loading="lazy" fetchpriority="high" alt=""
                      class="focus__circle-img"></div>
                  <div class="text-base focus__description-item-text">Наличие технологического стека</div>
                </li>
              </ul>
              <a href="/#submit-project" class="w-button button">Предложить проект</a>
            </div>
            <div class="section-gradient"></div>
          </div>
        </div>
      </section>
    </div>
  </div>
  <div id="ecosystem" class="is--bottom overlap-wrapper">
    <div id="about" class="section-anchor"></div>
    <div class="is--bottom overlap-wrapper"></div>
    <div class="is--bottom overlap-sticky">
      <section class="section is--ecosystem">
        <div class="ecosystem">
          <div class="container">
            <div class="section-header">
              <h2 class="text-xl section-header__title">Экосистема</h2>
              <div class="text-base section-header__description">Синергия независимого венчурного капитала и научной
                базы МФТИ, которая открывает технологическим компаниям доступ к передовым лабораториям, индустриальным
                партнёрам и сообществу.</div>
            </div>
          </div>
          <div class="is--eco-grid container swiper-wrapper">
            <div class="eco__cards-col">
              <div class="content-item__card swiper-slide is--eco">
                <div class="content-item__card-content is--center">
                  <div class="content-item__card-label is--center">Помощь в разработке</div>
                  <div class="content-item__card-details is--center">
                    <div class="w-richtext text-sm content-item__card-open-text is--center">
                      <p>Ашню Фонд усиливает команды практической экспертизой, помогая быстро масштабировать
                        технологические продукты и уверенно закрепляться в лидерах рынка.</p>
                    </div>
                  </div>
                  <div class="content-item__card-toggle-icon">
                    <div class="content-item-toggle-icon-line is--1"></div>
                    <div class="content-item-toggle-icon-line"></div>
                  </div>
                </div><img fetchpriority="high" src="images/eco_01-min.jpg" alt="" loading="lazy" sizes="100vw"
                  srcset="images/eco_01-min-p-500.jpg 500w, images/eco_01-min-p-800.jpg 800w, images/eco_01-min.jpg 1005w"
                  class="content-item__card-img">
              </div>
              <div class="content-item__card swiper-slide is--eco-small">
                <div class="content-item__card-content is--center">
                  <div class="content-item__card-label is--center">Доступ к лабораториям</div>
                  <div class="content-item__card-details is--center">
                    <div class="w-richtext text-sm content-item__card-open-text is--center">
                      <p>Компании получают доступ к лабораториям и технологическим ресурсам МФТИ, что позволяет глубже
                        проработать и быстрее протестировать научные и технологические гипотезы.</p>
                    </div>
                  </div>
                  <div class="content-item__card-toggle-icon">
                    <div class="content-item-toggle-icon-line is--1"></div>
                    <div class="content-item-toggle-icon-line"></div>
                  </div>
                </div><img fetchpriority="high" src="images/eco_03-min.jpg" alt="" loading="lazy" sizes="100vw"
                  srcset="images/eco_03-min-p-500.jpg 500w, images/eco_03-min-p-800.jpg 800w, images/eco_03-min.jpg 1005w"
                  class="content-item__card-img">
              </div>
            </div>
            <div class="eco__cards-col">
              <div class="content-item__card swiper-slide is--eco-small">
                <div class="content-item__card-content is--center">
                  <div class="content-item__card-label is--center">Выход на крупных заказчиков</div>
                  <div class="content-item__card-details is--center">
                    <div class="w-richtext text-sm content-item__card-open-text is--center">
                      <p>Мы помогаем стартапам быстро выйти на крупных корпоративных клиентов и масштабировать продажи,
                        используя возможности и партнёрскую сеть фонда и МФТИ.</p>
                    </div>
                  </div>
                  <div class="content-item__card-toggle-icon">
                    <div class="content-item-toggle-icon-line is--1"></div>
                    <div class="content-item-toggle-icon-line"></div>
                  </div>
                </div><img fetchpriority="high" src="images/eco_02-min.jpg" alt="" loading="lazy" sizes="100vw"
                  srcset="images/eco_02-min-p-500.jpg 500w, images/eco_02-min-p-800.jpg 800w, images/eco_02-min.jpg 1005w"
                  class="content-item__card-img">
              </div>
              <div class="content-item__card swiper-slide is--eco">
                <div class="content-item__card-content is--center">
                  <div class="content-item__card-label is--center">Налоговые привилегии</div>
                  <div class="content-item__card-details is--center">
                    <div class="w-richtext text-sm content-item__card-open-text is--center">
                      <p>
                        <a href="https://xn----8sbaogldcb1atek7cj3avf.xn--p1ai/" target="_blank">ИНТЦ «Долина
                          Физтеха»</a> — среда ускоренного роста для технологических компаний с доступом инфраструктуре
                        и налоговым преференциям.
                      </p>
                    </div>
                  </div>
                  <div class="content-item__card-toggle-icon">
                    <div class="content-item-toggle-icon-line is--1"></div>
                    <div class="content-item-toggle-icon-line"></div>
                  </div>
                </div><img fetchpriority="high" src="images/eco_04-min.jpg" alt="" loading="lazy" sizes="100vw"
                  srcset="images/eco_04-min-p-500.jpg 500w, images/eco_04-min-p-800.jpg 800w, images/eco_04-min.jpg 1005w"
                  class="content-item__card-img">
              </div>
            </div>
          </div>
          <div class="container">
            <div class="eco__description">
              <h3 class="eco__description-text">«Создаём будущее с теми, кто мыслит смело»</h3>
              <div class="text-base">Команда Ашню Фонда</div>
            </div>
          </div>
        </div>
        <div class="section-gradient is--ecosystem"></div>
      </section>
    </div>
  </div>
  <div class="footer-wrap is--home">
    <section class="is--relative section is--ecosystem">
      <div id="projects" class="section-anchor"></div>
      <div class="projects">
        <div class="container">
          <div class="section-header">
            <h2 class="text-xl section-header__title">Проекты</h2>
            <div class="text-base section-header__description">В нашем портфеле — проекты с научной основой и большим
              рыночным потенциалом.</div>
          </div>
        </div>
        <div class="is--eco-grid container swiper-wrapper">
          <div class="content-item__card swiper-slide is--eco">
            <div class="content-item__card-content is--center">
              <div class="content-item__card-label is--center">СейсмикЛаб</div>
              <div class="content-item__card-details is--center">
                <div class="text-sm content-item__card-open-text is--center">Технологическая компания, разрабатывающая
                  продукты для горной и обогатительной промышленности. Платформа и датчики для контроля загрузки
                  мельниц, которые повышают эффективность и снижают издержки на переработке руды.</div>
              </div>
              <div class="content-item__card-toggle-icon">
                <div class="content-item-toggle-icon-line is--1"></div>
                <div class="content-item-toggle-icon-line"></div>
              </div>
            </div><img width="Auto" fetchpriority="high" alt="" loading="lazy" src="images/seismic-lab-min.jpg"
              sizes="100vw"
              srcset="images/seismic-lab-min-p-500.jpg 500w, images/seismic-lab-min-p-800.jpg 800w, images/seismic-lab-min-p-1080.jpg 1080w, images/seismic-lab-min.jpg 2016w"
              class="content-item__card-img">
          </div>
          <div class="content-item__card swiper-slide is--eco">
            <div class="content-item__card-content is--center">
              <div class="content-item__card-label is--center">Маслов</div>
              <div class="content-item__card-details is--center">
                <div class="text-sm content-item__card-open-text is--center">Интеллектуальные роботы для автоматизации
                  молочных ферм и ИИ система управления стадом для контроля за животными.</div>
              </div>
              <div class="content-item__card-toggle-icon">
                <div class="content-item-toggle-icon-line is--1"></div>
                <div class="content-item-toggle-icon-line"></div>
              </div>
            </div><img width="Auto" fetchpriority="high" alt="" loading="lazy" src="images/maslov-min.jpg" sizes="100vw"
              srcset="images/maslov-min-p-500.jpg 500w, images/maslov-min-p-800.jpg 800w, images/maslov-min-p-1080.jpg 1080w, images/maslov-min-p-1600.jpg 1600w, images/maslov-min.jpg 2016w"
              class="content-item__card-img">
          </div>
        </div>

        <div id="submit-project" class="container submit-project">
          <div class="submit-project__header">
            <h3 class="text-lg submit-project__title">Предложить проект</h3>
          </div>

          <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
            <symbol id="icon-close" viewBox="0 0 20 20">
              <path fill="currentColor"
                d="M17.78 3.636 11.413 10l6.365 6.364-1.414 1.415L10 11.414l-6.363 6.363-1.414-1.414L8.586 10 2.223 3.637l1.414-1.414L10 8.585l6.365-6.364 1.414 1.415Z" />
            </symbol>
          </svg>

          <template id="file-preview-template">
            <div class="file-input__preview">
              <span class="file-input__preview-name"></span>
              <button class="file-input__preview-remove" type="button" title="Удалить файл">
                <svg width="100%" height="100%">
                  <use href="#icon-close"></use>
                </svg>
              </button>
            </div>
          </template>

          <form id="submit-project-form" class="submit-project-form" action="" novalidate>
            <label>
              <div class="form-input-wrapper">
                <input name="name" class="form-input" type="name" placeholder=" " required>
                <div class="input-placeholder text-sm">
                  ФИО *
                </div>
              </div>
              <div class="form-input-error text-xs">
              </div>
            </label>

            <label>
              <div class="form-input-wrapper">
                <input name="phone" class="form-input" type="tel" placeholder=" " required>
                <div class="input-placeholder text-sm">
                  Телефон *
                </div>
              </div>
              <div class="form-input-error text-xs">
              </div>
            </label>

            <label>
              <div class="form-input-wrapper">
                <input name="email" class="form-input" type="email" placeholder=" " required>
                <div class="input-placeholder text-sm">
                  Почта *
                </div>
              </div>
              <div class="form-input-error text-xs">
              </div>
            </label>

            <label>
              <div class="form-input-wrapper">
                <textarea name="message" maxlength="2000" class="form-input" placeholder=" "></textarea>
                <div class="input-placeholder text-sm">
                  Сообщение
                </div>
              </div>
            </label>

            <div class="form-input__file-wrapper">
              <div class="form-input__upload-description text-sm">
                Файлы
              </div>

              <div class="file-input__preview-wrapper">
              </div>

              <div class="form-input-error text-xs">
              </div>

              <label>
                <button class="form-input form-input--upload" id="uploadButton" type="button">
                  <span class="text-xs form-input__upload-description">
                    До 5 файлов, каждый не более 20 Мб.
                  </span>
                  <div class="file-input__button">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20">
                      <path fill="currentColor"
                        d="M18 19H3v-2h15v2Zm.264-11.222L16.85 9.192l-5.365-5.364v11.143h-2V3.828L4.121 9.192 2.707 7.778 10.485 0l7.779 7.778Z" />
                    </svg>
                  </div>
                </button>
                <input class="file-input" type="file" id="fileInput" hidden multiple>

              </label>

            </div>

            <div class="form-checkboxes">
              <label class="form-checkbox">
                <input class="form-checkbox__input" type="checkbox" name="consent" required>
                <span class="text-sm form-checkbox__label text-xs">Даю <a href="/soglasie.html"
                    class="footer__text form-checkbox__link">согласие на обработку персональных данных</a>, в том числе
                  с использованием стороннего сервиса Яндекс.Формы
                </span>
              </label>
              <label class="form-checkbox">
                <input class="form-checkbox__input" type="checkbox" name="consent" required>
                <span class="text-sm form-checkbox__label text-xs">Ознакомлен и принимаю <a href="/politika.html"
                    class="footer__text form-checkbox__link">Политику конфиденциальности</a> и <a
                    href="/soglashenie.html" class="footer__text form-checkbox__link">Пользовательское
                    соглашение</a></span>
              </label>
            </div>

            <button type="submit" style="width: fit-content; margin: 0 auto; margin-top: 52px;"
              class="w-button button">Отправить</button>

          </form>

          <div class="form-success-message">
            <h3 class="text-lg">Заявка отправлена</h3>
            <p class="text-md" style="margin-top: 16px">Спасибо! Мы обязательно с вами свяжемся.
            </p>
          </div>
        </div>
      </div>
    </section>
    <div class="section is--footer">
      <footer class="footer">
        <div class="container">
          <div class="footer__wrap">
            <div class="footer__cell">
              <div class="w-embed footer__logo"><svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                  fill="none" viewbox="0 0 92 92">
                  <path fill="#000"
                    d="M12.34 80.485c6.133 5.938 12.332 10.133 14.979 7.293 2.454-2.582-.904-8.068-5.488-14.458 15.754 13.36 28.926 21.752 32.994 17.62 4.39-4.518-5.36-18.008-17.885-32.79 16.013 14.717 36.093 28.207 42.743 21.494 6.715-6.712-6.908-25.947-21.759-43.181C74.583 50.858 86.657 59.12 90.918 54.989c4.133-4.131-3.938-17.04-18.014-33.952 5.553 4.712 12.332 9.037 14.915 6.39 2.518-2.646-1.55-9.553-8.2-15.878l-.065-.065C73.55 5.546 67.287 1.286 64.574 4.126c-2.453 2.581 2.002 9.294 5.683 14.329C54.308 5.417 41.137-3.039 37.133 1.027c-4.326 4.519 5.424 17.686 18.144 32.468C39.07 18.778 18.925 5.61 12.274 12.323c-6.65 6.713 7.038 25.432 21.695 42.86C17.44 40.658 5.366 32.72 1.104 36.98c-4.197 4.13 3.939 16.911 18.144 33.758-5.682-4.583-12.397-8.908-14.98-6.261-2.583 2.71 1.485 9.617 7.942 15.943l.13.065Z">
                  </path>
                </svg></div>
              <div class="text-xs footer__accuraten">Designed by <a href="https://accuraten.com/"
                  target="_blank">Accuraten</a>
              </div>
            </div>
            <div class="footer__cell">
              <a href="mailto:<EMAIL>" class="text-xs footer__text is--email"><EMAIL></a>
              <div class="footer__policy-links">
                <a href="politika.html" class="text-sm footer__text">Политика конфиденциальности</a>
                <a href="soglashenie.html" class="text-sm footer__text">Пользовательское соглашение</a>
              </div>
              <div class="text-xs footer__text is--copyright">©2025. ООО Ашню</div>
            </div>
          </div>
        </div>
      </footer>
      <div class="footer__animation"><video src="https://ashnu.ru/ashnu-footer-stream.mp4" playsinline="" muted=""
          loop="" preload="none" poster="https://ashnu.ru/ashnu-footer-poster.png" class="footer__video"></video><img
          src="images/footer-mobile.png" loading="eager" sizes="100vw"
          srcset="images/footer-mobile-p-500.png 500w, images/footer-mobile-p-800.png 800w, images/footer-mobile.png 1024w"
          alt="" class="footer__mobile-img"></div>
    </div>
  </div>
  <script src="/js/jquery-3.5.1.min.js" type="text/javascript"></script>
  <script src="js/ashnu.js" type="text/javascript"></script>
  <script fetchpriority="high" type="text/javascript" src="/js/gsap.min.js"></script>
  <script fetchpriority="high" type="text/javascript" src="/js/ScrollTrigger.min.js"></script>
  <script fetchpriority="high" type="text/javascript" src="/js/swiper-bundle.min.js"></script>

  <script>
    //#region [Form]
    //#region [File upload]
    const fileInput = document.querySelector('.file-input');
    const fileInputError = document.querySelector('.form-input__file-wrapper .form-input-error');
    const previewWrapper = document.querySelector('.file-input__preview-wrapper');
    const filePreviewTemplate = document.getElementById('file-preview-template');
    const fileUploadButton = document.getElementById('uploadButton');
    const files = [];
    let fileUploadValid = true;

    fileUploadButton.addEventListener('click', () => {
      fileInput.click();
    });

    // Prevent default behavior for drag and drop events
    fileUploadButton.addEventListener('dragover', (e) => {
      e.preventDefault();
    });

    fileUploadButton.addEventListener('drop', (e) => {
      e.preventDefault();
      const droppedFiles = e.dataTransfer.files;
      handleAddFiles(droppedFiles);
    });

    fileInput.addEventListener('change', (e) => {
      const newFiles = e.target.files;
      handleAddFiles(newFiles);
    });

    function handleAddFiles(newFiles) {
      previewWrapper.innerHTML = '';
      files.push(...newFiles);

      for (const file of files) {
        const preview = filePreviewTemplate.content.cloneNode(true);
        preview.querySelector('.file-input__preview-name').textContent = file.name;
        preview.querySelector('.file-input__preview-remove')
          .addEventListener('click', () => handleRemoveFile(file));

        fileInputError.textContent = "";
        fileUploadValid = true;


        if (files.length > 5) {
          fileInputError.textContent = 'Максимальное количество файлов - 5';
          fileUploadValid = false;
        }

        if (file.size > 20000000) {
          fileInputError.textContent = 'Максимальный размер файла - 20 Мб';
          preview.querySelector('.file-input__preview-name').style.color = 'red';
          fileUploadValid = false;
        }

        previewWrapper.appendChild(preview);
      }

      // Clear the input value to allow selecting the same file again
      fileInput.value = '';
    }

    function handleRemoveFile(file) {
      const index = files.indexOf(file);
      files.splice(index, 1);
      handleAddFiles([]);
    }
    //#endregion [File upload]

    const form = document.querySelector('#submit-project-form');

    form.addEventListener('submit', (e) => {
      e.preventDefault();
      let isValid = true;

      form.querySelectorAll('input').forEach(input => {
        const $errorMessage = input.closest('label').querySelector('.form-input-error');

        console.log(input);
        console.log(input.value);
        input.addEventListener('input', () => {
          input.classList.remove('invalid');
          if ($errorMessage); {
            $errorMessage.textContent = '';
          }
        });

        if (!input.value.length && !input.checkValidity()) {
          input.classList.add('invalid');
          isValid = false;
          if ($errorMessage) {
            $errorMessage.textContent = 'Поле не заполнено';
          }
        } else if (input.value.length && !input.checkValidity()) {
          input.classList.add('invalid');
          isValid = false;
          if ($errorMessage) {
            $errorMessage.textContent = 'Некорректный формат';
          }
        }

        if (!fileUploadValid) {
          isValid = false;
        }
      });

      if (!isValid) return;

      const formData = new FormData(form);
      for (let i = 0; i < files.length; i++) {
        formData.append('files[]', files[i]); 
      }

      function showSuccessMessage() {
        form.style.display = 'none';
        document.querySelector('.form-success-message').style.display = 'block';
        document.querySelector('.submit-project__header').style.display = 'none';
      }

      fetch("/send.php", {
        method: 'POST',
        body: formData
      })
        .then(response => response.json())
        .then(data => {
          console.log(data)
          if (data.status === "success") {
            //showSuccessMessage()
            alert('успеx');
          } else {
            alert('Упс что пошло не так. Попробуйте позже!')
          }
        })
        .catch(error => {
          console.error(error);
          alert('Упс что пошло не так. Попробуйте позже!')
        });
    });

    //#endregion [Form]
  </script>

  <script>
    //#region [Header]
    const heroEl = document.querySelector('.section.is--hero')
    const headerEl = document.querySelector('.container.is--header')
    // Hide menu after hero and reveal back on scroll up
    const actionNav = gsap.to(headerEl, { y: '-100%', duration: 0.3, ease: 'power2.inOut', paused: true });
    // Change menu after hero
    new ScrollTrigger({
      trigger: heroEl,
      start: 'top top',
      end: 'bottom top',
      onLeave: () => {
        headerEl.classList.add('is--scrolled');
        actionNav.play();
      },
      onEnterBack: () => {
        headerEl.classList.remove('is--scrolled');
        actionNav.reverse();
      },
    })
    new ScrollTrigger({
      trigger: document.body,
      start: 'top top',
      end: 'bottom top',
      onUpdate: ({ progress, direction }) => {
        if (direction === 1 && !ScrollTrigger.isInViewport(heroEl)) {
          actionNav.play();
        } else if (direction === -1) {
          actionNav.reverse();
        }
      }
    })
    //#endregion [Header]
    //#region [Mobile menu]
    const mobileHeader = document.querySelector('.header');
    const mobileMenuTriggerButton = document.querySelector('.header__mobile-menu-button');
    const headerLinks = document.querySelectorAll('.header__menu-link');
    if (mobileHeader && mobileMenuTriggerButton) {
      mobileMenuTriggerButton.addEventListener('click', () => {
        mobileHeader.classList.toggle('is--open');
      });
      headerLinks.forEach((link) => {
        link.addEventListener('click', () => {
          mobileHeader.classList.remove('is--open');
        })
      })
    }
    //#endregion [Mobile menu]
  </script>
  <script>
    function isMobileWidth(width) {
      return width <= 768;
    }
    // Debounce function
    function debounce(func, wait) {
      let timeout;
      return function () {
        clearTimeout(timeout);
        timeout = setTimeout(func, wait);
      }
    }
    window.addEventListener("load", () => {
      const videos = document.querySelectorAll("video[muted]");
      videos.forEach(video => {
        if ((!isMobileWidth(window.innerWidth) && video.classList.contains('hero__mobile-video')) || (isMobileWidth(window.innerWidth) && video.classList.contains('hero__desktop-video'))) { return }
        video.play().catch(err => {
          console.log("No video autoplay:", err);
        });
      });
    });
    const cardsArr = document.querySelectorAll('.content-item__card');
    //#region [Blurred cards]
    cardsArr.forEach((card) => {
      new ScrollTrigger({
        trigger: card,
        start: '30% bottom',
        onEnter: () => card.classList.add('is--visible'),
        onEnterBack: () => card.classList.add('is--visible')
      })
    })
    //#endregion [Blurred cards]
    //#region [Section gradient on scroll]
    const sectionGradients = document.querySelectorAll('.section-gradient')
    sectionGradients.forEach((gradient) => {
      gsap.to(gradient, {
        scrollTrigger: {
          trigger: gradient,
          start: "bottom 50%",
          end: 'bottom top',
          scrub: true,
        },
        opacity: 1
      })
    })
    //#endregion [Section gradient on scroll]
    //#region [Focus slider]
    let focusSlider = null;
    let previousWidth = window.innerWidth;
    function initFocusSlider() {
      if (focusSlider) {
        focusSlider.destroy();
        focusSlider = null;
      }
      focusSlider = new Swiper('.focus__cards-wrapper.swiper', {
        slidesPerView: 'auto',
        spaceBetween: 16,
      });
    }
    function destroyTestsSlider() {
      if (focusSlider) {
        focusSlider.destroy();
        focusSlider = null;
      }
    }
    function handleSliderResize() {
      const currentWidth = window.innerWidth;
      const wasMobile = isMobileWidth(previousWidth);
      const isMobile = isMobileWidth(currentWidth);
      // Only act if we're crossing the mobile/desktop boundary
      if (wasMobile !== isMobile) {
        if (isMobile && !focusSlider) {
          initFocusSlider();
        } else if (!isMobile && focusSlider) {
          destroyTestsSlider();
        }
      }
      previousWidth = currentWidth;
    }
    // Initialize based on current viewport
    if (isMobileWidth(window.innerWidth)) {
      initFocusSlider();
    }
    // Add debounced resize listener
    const debouncedResize = debounce(handleSliderResize, 300);
    window.addEventListener("resize", debouncedResize);
    //#endregion [Focus slider]
    //#region [Card details]
    cardsArr.forEach((card) => {
      const cardContent = card.querySelector('.content-item__card-content');
      // Handle click for mobile
      card.addEventListener('click', (e) => {
        if (isMobileWidth(window.innerWidth)) {
          cardContent.classList.toggle('is--open');
        }
      });
      // Handle hover for desktop
      card.addEventListener('mouseenter', () => {
        if (!isMobileWidth(window.innerWidth)) {
          cardContent.classList.add('is--open');
        }
      });
      card.addEventListener('mouseleave', () => {
        if (!isMobileWidth(window.innerWidth)) {
          cardContent.classList.remove('is--open');
        }
      });
    });
    //#endregion [Card details]
  </script>
</body>

</html>