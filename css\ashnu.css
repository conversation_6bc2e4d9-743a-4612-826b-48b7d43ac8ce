@font-face {
  font-family: SFT Schrifted Sans;
  src: url('../fonts/SFTSchriftedSans-Regular.woff2') format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: SFT Schrifted Sans;
  src: url('../fonts/SFTSchriftedSans-Medium.woff2') format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

:root {
  --black: black;
  --white: #fff;
  --header-height: 5.56rem;
  --transparent: transparent;
}

.w-layout-vflex {
  flex-direction: column;
  align-items: flex-start;
  display: flex;
}

body {
  color: var(--black);
  font-family: SFT Schrifted Sans, Arial, sans-serif;
  font-size: 1.81rem;
  line-height: 132%;
}

h1 {
  max-width: 818px;
  margin: 0 auto;
  font-size: 4.72rem;
  font-weight: 500;
  line-height: 108%;
}

h2 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 3.08rem;
  font-weight: 400;
  line-height: 132%;
}

h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 3.06rem;
  font-weight: 500;
  line-height: 116%;
}

h4 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.81rem;
  font-weight: 500;
  line-height: 132%;
}

a {
  text-decoration: none;
}

.custom-code {
  display: none;
}

.section {
  background-color: var(--white);
  overflow: hidden;
}

.section.is--header {
  height: var(--header-height);
}

.section.is--hero {
  z-index: 0;
  margin-top: calc(var(--header-height) * -1);
  height: 100vh;
  position: relative;
}

.section.is--footer {
  z-index: 10;
  position: relative;
  overflow: visible;
}

.section.is--relative {
  z-index: 20;
  position: relative;
}

.section.is--relative.is--ecosystem {
  z-index: 20;
  background-color: var(--transparent);
}

.section.is--policy {
  margin-top: 120px;
  margin-bottom: 140px;
}

.section.is--ecosystem {
  z-index: 10;
  position: relative;
}

.container {
  width: 100%;
  max-width: 100rem;
  margin-left: auto;
  margin-right: auto;
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.container.is--header {
  z-index: 1000;
  max-width: none;
  transition: background-color .5s cubic-bezier(.645, .045, .355, 1);
  position: fixed;
  top: 0%;
  left: 0%;
  right: 0%;
}

.container.is--hero {
  z-index: 10;
  margin-top: var(--header-height);
  justify-content: flex-start;
  align-items: center;
  height: 100%;
  position: relative;
}

.container.is--focus-grid.swiper-wrapper {
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  padding-left: 0;
  padding-right: 0;
  display: grid;
}

.container.is--eco-grid.swiper-wrapper {
  box-sizing: border-box;
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start;
  display: grid;
}

.container.is--projects-button {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  margin-top: 4.72rem;
  padding-bottom: 9.72rem;
  display: flex;
}

.header {
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 95rem;
  margin-left: auto;
  margin-right: auto;
  padding-top: 1.67rem;
  padding-bottom: 1.67rem;
  display: flex;
}

.header__logo-wrap {
  justify-content: flex-start;
  align-items: center;
  width: 16.39rem;
  height: 2.15rem;
  display: flex;
}

.header__menu-wrapper {
  grid-column-gap: 3.33rem;
  grid-row-gap: 3.33rem;
  justify-content: flex-end;
  align-items: center;
  margin-top: 0;
  margin-bottom: 0;
  padding-left: 0;
  list-style-type: none;
  display: flex;
}

.header__menu-link {
  text-decoration: none;
  transition: color .25s cubic-bezier(.645, .045, .355, 1);
}

.header__menu-link:hover {
  color: #0009;
}

.header__mobile-menu-button {
  grid-column-gap: 6px;
  grid-row-gap: 6px;
  background-color: var(--transparent);
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 30px;
  padding: 0;
  display: none;
}

.header__menu-burger-icon-line {
  background-color: var(--black);
  width: 100%;
  height: 2px;
  display: block;
}

.text-xl {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 4.72rem;
  font-weight: 500;
  line-height: 108%;
}

.text-lg {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 3.06rem;
  font-weight: 400;
  line-height: 116%;
}

.text-base {
  font-size: 1.81rem;
  line-height: 132%;
}

.text-md {
  font-size: 1.81rem;
  line-height: 128%;
}

.text-sm {
  font-size: 1.39rem;
  line-height: 132%;
}

.hero {
  grid-column-gap: 1.39rem;
  grid-row-gap: 1.39rem;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  max-width: 54.65rem;
  height: 100%;
  display: flex;
}

.section-header {
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  max-width: 80.56rem;
  padding-top: 9.72rem;
  padding-bottom: 3.61rem;
  display: flex;
}

.content-item__card {
  cursor: default;
}

.content-item__card.swiper-slide {
  z-index: 0;
  aspect-ratio: 440 / 520;
  object-fit: cover;
  background-color: #0000;
  border-radius: 1.39rem;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.content-item__card.swiper-slide.is--other {
  background-color: var(--white);
  border: 1px solid #000;
}

.content-item__card.swiper-slide.is--eco {
  aspect-ratio: 1;
}

.content-item__card.swiper-slide.is--eco-small {
  aspect-ratio: 672 / 550;
}

.content-item__card.swiper-slide.is--focus {
  text-align: left;
  padding: 0;
}

.content-item__card-img {
  z-index: 0;
  aspect-ratio: 440 / 520;
  object-fit: cover;
  border-radius: 1.39rem;
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
}

.content-item__card-content {
  z-index: 10;
  color: var(--white);
  display: flex;
  position: absolute;
  inset: 0%;
}

.content-item__card-content.is--other {
  cursor: auto;
  flex-flow: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
  position: static;
}

.content-item__card-content.is--other.text-sm {
  color: var(--black);
  padding: 1.67rem;
}

.content-item__card-content.is--center {
  justify-content: center;
  align-items: center;
}

.content-item__card-details {
  z-index: 10;
  opacity: 0;
  -webkit-backdrop-filter: blur(70px);
  backdrop-filter: blur(70px);
  background-color: #0006;
  flex-flow: column;
  width: 100%;
  height: 100%;
  padding: 1.67rem;
  transition: all .3s;
  display: flex;
  position: absolute;
  inset: 0%;
}

.content-item__card-details.is--center {
  justify-content: center;
  align-items: center;
  padding-left: 5rem;
  padding-right: 5rem;
}

.content-item__card-label {
  z-index: 0;
  margin-top: auto;
  margin-bottom: 0;
  padding: 1.67rem;
  font-weight: 500;
  transition: opacity .3s cubic-bezier(.645, .045, .355, 1);
  position: relative;
}

.content-item__card-label.is--center {
  text-align: center;
  margin-top: 0;
}

.content-item__card-open-text {
  z-index: 10;
  color: #ffffffa3;
  margin-top: auto;
}

.content-item__card-open-text.text-sm.is--center {
  margin-top: 0;
}

.content-item__card-toggle-icon {
  z-index: 100;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 1.39rem;
  height: 1.39rem;
  display: flex;
  position: absolute;
  inset: 1.67rem 1.67rem auto auto;
}

.content-item-toggle-icon-line {
  background-color: #ffffffa3;
  width: 100%;
  height: 2px;
  transition: transform .3s cubic-bezier(.645, .045, .355, 1);
  position: absolute;
  left: 0%;
  right: 0%;
}

.content-item-toggle-icon-line.is--1 {
  transform: rotate(-90deg);
}

.focus__card-content-list {
  grid-column-gap: .28rem;
  grid-row-gap: .28rem;
  flex-flow: column;
  margin-bottom: 0;
  padding-left: 1.4rem;
  display: flex;
}

.focus__description {
  text-align: center;
  margin-top: 9.72rem;
  padding-bottom: 6.94rem;
}

.focus__description-text {
  max-width: 75rem;
  margin-left: auto;
  margin-right: auto;
}

.focus__description-list {
  justify-content: space-between;
  align-items: center;
  margin-top: 6.94rem;
  margin-bottom: 6.94rem;
  padding-left: 0;
  list-style-type: none;
  display: flex;
}

.focus__description-item {
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  padding-left: 1.87rem;
  padding-right: 3.06rem;
  display: flex;
}

.focus__description-item-mask {
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 8.33rem;
  height: 8.33rem;
  display: flex;
  position: relative;
  overflow: hidden;
}

.focus__description-item-text.text-base {
  font-weight: 500;
  line-height: 128%;
}

.button {
  background-color: var(--black);
  border: 1px solid #000;
  border-radius: 10000px;
  justify-content: center;
  align-items: center;
  padding: 1.53rem 5rem;
  line-height: 100%;
  transition: color .3s cubic-bezier(.645, .045, .355, 1), background-color .3s cubic-bezier(.645, .045, .355, 1);
  display: inline-flex;
}

.button:hover {
  background-color: var(--white);
  color: var(--black);
}

.eco__cards-col {
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  flex-flow: column;
  width: 100%;
  display: flex;
}

.eco__description {
  grid-column-gap: 2.5rem;
  grid-row-gap: 2.5rem;
  text-align: center;
  flex-flow: column;
  max-width: 52.36rem;
  margin-top: 9.72rem;
  margin-left: auto;
  margin-right: auto;
  padding-bottom: 6.94rem;
  display: flex;
}

.content-item__card-link {
  z-index: 100;
  text-decoration: none;
  position: absolute;
  inset: auto 1.67rem 1.67rem auto;
}

.content-item__card-link.text-sm {
  grid-column-gap: .4rem;
  grid-row-gap: .4rem;
  justify-content: flex-start;
  align-items: center;
  transition: opacity .3s cubic-bezier(.645, .045, .355, 1);
  display: flex;
}

.content-item__card-link.text-sm:hover {
  opacity: .64;
}

.content-item__card-link-icon {
  opacity: .64;
  justify-content: center;
  align-items: center;
  width: 1.39rem;
  height: 1.39rem;
  display: flex;
}

.footer {
  z-index: 10;
  padding-bottom: 2.08rem;
  position: relative;
}

.footer__logo {
  width: 6.39rem;
  height: 6.39rem;
}

.footer__wrap {
  grid-column-gap: 1.67rem;
  grid-row-gap: 1.67rem;
  border-top: 1px solid #000;
  padding-top: 9.72rem;
  display: flex;
}

.footer__accuraten.text-xs {
  margin-top: auto;
}

.text-xs {
  font-size: 1.11rem;
  line-height: 120%;
}

.footer__cell {
  flex-flow: column;
  flex: 1;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
}

.footer__text.is--copyright {
  margin-bottom: .56rem;
}

.footer__policy-links {
  grid-column-gap: .56rem;
  grid-row-gap: .56rem;
  flex-flow: column;
  margin-top: 3.61rem;
  margin-bottom: 9.72rem;
  display: flex;
}

.eco__description-text {
  max-width: 75rem;
  margin-left: auto;
  margin-right: auto;
}

.section-gradient {
  z-index: 0;
  opacity: 0;
  pointer-events: none;
  background-image: linear-gradient(#fff0, #00000012);
  width: 100%;
  height: 40rem;
  position: absolute;
  bottom: 0%;
  left: 0%;
  right: 0%;
}

.hero__animation {
  z-index: 0;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  max-width: 1800px;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  position: absolute;
  inset: 0%;
}

.policy__rich p {
  margin-bottom: 26px;
  font-size: 26px;
  line-height: 132%;
}

.policy__rich h2 {
  text-align: center;
  margin-top: 100px;
  margin-bottom: 52px;
}

.policy__rich h1 {
  text-align: center;
  margin-bottom: 100px;
}

.utility-page-wrap {
  justify-content: center;
  align-items: center;
  width: 100vw;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  display: flex;
}

.utility-page-content {
  text-align: center;
  flex-direction: column;
  width: 260px;
  display: flex;
}

.page-404 {
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.body-404 {
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  display: flex;
}

.page-404__title {
  margin-bottom: 24px;
}

.overlap-wrapper {
  z-index: 0;
  min-height: 200vh;
  margin-bottom: -100vh;
  position: relative;
}

.overlap-wrapper.is--bottom {
  min-height: 200vh;
  margin-bottom: -100vh;
  bottom: 0;
}

.overlap-sticky {
  z-index: 0;
  position: sticky;
  top: 0;
}

.overlap-sticky.is--bottom {
  top: auto;
  bottom: 50vh;
}

.footer__animation {
  z-index: 0;
  pointer-events: none;
  justify-content: center;
  align-items: flex-end;
  width: 100%;
  display: flex;
  position: absolute;
  bottom: 0%;
  left: 0%;
  right: 0%;
}

.hero__animation-canvas {
  display: block;
  position: relative;
}

.hero__mobile-video {
  display: none;
}

.footer__animation-canvas {
  display: block;
}

.footer__mobile-img {
  display: none;
}

.focus__circle-canvas {
  pointer-events: none;
  width: 100%;
  height: 100%;
  cursor: auto !important;
}

.footer-wrap {
  overflow: hidden;
}

.footer-wrap.is--home {
  z-index: 20;
  background-color: var(--white);
  position: relative;
}

.focus__circle-img {
  z-index: 10;
  display: block;
  position: absolute;
  inset: 0%;
}

.img {
  position: relative;
}

.img.hero__3d-plceholder {
  z-index: 10;
  flex: none;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.hero__desktop-video {
  flex: none;
  width: 100%;
  max-height: 980px;
  position: relative;
  left: 35%;
}

.focus__circle-video {
  z-index: 15;
  object-fit: fill;
  flex: none;
  width: 100%;
  height: 100%;
  position: absolute;
}

.focus__circle-video.is--2 {
  aspect-ratio: 1;
  left: auto;
}

.focus__circle-video.is--3 {
  aspect-ratio: 1;
  width: 100%;
  height: 100%;
  left: auto;
}

.footer__video {
  z-index: 0;
  object-fit: fill;
  flex: none;
  max-width: 1800px;
  max-height: 600px;
  margin-left: auto;
  margin-right: auto;
  position: absolute;
  bottom: 0;
}

.footer__video.is--2 {
  aspect-ratio: 1;
  left: auto;
}

.footer__video.is--3 {
  aspect-ratio: 1;
  width: 100%;
  height: 100%;
  left: auto;
}

.section-anchor {
  visibility: hidden;
  position: absolute;
  top: 60px;
}

.section-anchor.is--focus {
  top: 20px;
}

@media screen and (max-width: 991px) {
  .container {
    padding-left: 24px;
    padding-right: 24px;
  }

  .container.is--header {
    padding-left: 12px;
    padding-right: 12px;
  }

  .container.is--focus-grid.swiper-wrapper {
    grid-template-columns: 1fr 1fr;
  }

  .container.is--eco-grid.swiper-wrapper {
    grid-column-gap: 1.67rem;
    grid-row-gap: 1.67rem;
    grid-auto-columns: 1fr;
    display: flex;
  }

  .container.is--projects-button {
    padding-bottom: 100px;
  }

  .header {
    border-radius: 8px;
    flex-flow: column;
    grid-template-rows: auto 0fr;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 10px;
    padding: 12px;
    transition-property: all;
    transition-duration: .5s;
    transition-timing-function: cubic-bezier(.645, .045, .355, 1);
    display: grid;
    overflow: hidden;
  }

  .header.is--open {
    grid-template-rows: auto 1fr;
  }

  .header__menu {
    width: 100%;
    overflow: hidden;
  }

  .header__menu-wrapper {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    padding-top: 20px;
  }

  .header__menu-item {
    border-top: 1px solid #0000000a;
    width: 100%;
  }

  .header__menu-link {
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .header__mobile-menu-button {
    height: 18px;
    display: flex;
    position: relative;
  }

  .header__menu-burger-icon-line {
    padding: 0;
    transition: opacity .3s cubic-bezier(.645, .045, .355, 1), bottom .3s cubic-bezier(.645, .045, .355, 1), top .3s cubic-bezier(.645, .045, .355, 1);
  }

  .header__menu-burger-icon-line.is--1 {
    position: absolute;
    top: 0%;
    left: 0%;
    right: 0%;
  }

  .header__menu-burger-icon-line.is--3 {
    position: absolute;
    bottom: 0%;
    left: 0%;
    right: 0%;
  }

  .section-header {
    padding-top: 100px;
    padding-bottom: 36px;
  }

  .content-item__card.swiper-slide.is--eco, .content-item__card.swiper-slide.is--eco-small {
    aspect-ratio: 2 / 3;
  }

  .content-item__card-label {
    font-size: 20px;
    line-height: 120%;
  }

  .focus__description-list {
    grid-column-gap: 3.61rem;
    grid-row-gap: 3.61rem;
    flex-flow: column;
  }

  .focus__description-item {
    padding-left: 13.27rem;
    padding-right: 13.27rem;
  }

  .button {
    font-size: 1.11rem;
  }

  .header__top {
    flex-flow: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .hero__desktop-video, .focus__circle-video {
    width: 100%;
  }

  .footer__video {
    width: 160%;
  }
}

@media screen and (max-width: 767px) {
  h1 {
    font-size: 36px;
    line-height: 104%;
  }

  h2 {
    font-size: 24px;
    line-height: 104%;
  }

  h3 {
    font-size: 24px;
    line-height: 120%;
  }

  h4 {
    font-size: 20px;
    line-height: 120%;
  }

  .section.is--hero {
    height: 100vh;
  }

  .container.is--hero {
    padding-bottom: var(--header-height);
  }

  .container.is--focus-grid.swiper-wrapper {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    display: flex;
    overflow: visible;
  }

  .container.is--eco-grid.swiper-wrapper {
    box-sizing: border-box;
    flex-flow: column;
  }

  .container.is--projects-button {
    margin-top: 52px;
  }

  .header__logo-wrap {
    width: 177px;
    height: 24px;
  }

  .header__mobile-menu-button {
    display: flex;
  }

  .text-xl {
    font-size: 36px;
    line-height: 104%;
  }

  .text-lg {
    font-size: 24px;
    line-height: 120%;
  }

  .text-base {
    font-size: 20px;
  }

  .text-md {
    font-size: 20px;
    line-height: 120%;
  }

  .text-sm {
    font-size: 16px;
  }

  .hero {
    justify-content: flex-end;
    align-items: flex-start;
    padding-bottom: 120px;
  }

  .focus__cards-wrapper.swiper {
    overflow: visible;
  }

  .content-item__card.swiper-slide {
    aspect-ratio: 2 / 3;
    flex: none;
    width: 100%;
  }

  .content-item__card.swiper-slide.is--other {
    aspect-ratio: 300 / 400;
    height: 400px;
  }

  .content-item__card.swiper-slide.is--eco {
    aspect-ratio: 335 / 420;
  }

  .content-item__card.swiper-slide.is--eco-small {
    aspect-ratio: 335 / 400;
  }

  .content-item__card.swiper-slide.is--focus {
    aspect-ratio: 300 / 400;
    width: 85%;
    height: 400px;
  }

  .content-item__card-details {
    padding: 24px 32px;
  }

  .content-item__card-details.is--center {
    padding: 72px 32px;
  }

  .focus__description {
    padding-bottom: 0;
  }

  .focus__description-text {
    text-align: center;
  }

  .focus__description-list {
    grid-column-gap: 3.61rem;
    grid-row-gap: 3.61rem;
    margin-top: 52px;
    margin-bottom: 52px;
  }

  .focus__description-item {
    padding-left: .67rem;
    padding-right: .67rem;
  }

  .focus__description-item-mask {
    width: 100px;
    height: 100px;
  }

  .focus__description-item-text {
    max-width: 275px;
  }

  .button {
    width: 100%;
    min-height: 72px;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .eco__cards-col {
    grid-column-gap: 20px;
    grid-row-gap: 20px;
  }

  .footer {
    z-index: 10;
  }

  .footer__logo {
    grid-area: 3 / 2 / 3 / span2;
    width: 44px;
    height: 44px;
    margin-left: auto;
  }

  .footer__wrap {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    align-items: start;
    width: 100%;
    padding-top: 72px;
    display: grid;
  }

  .footer__accuraten.text-xs {
    grid-area: 3 / 1 / 4 / span2;
    align-self: end;
    margin-top: 0;
  }

  .text-xs {
    font-size: 16px;
  }

  .footer__cell {
    display: contents;
  }

  .footer__text.text-xs {
    grid-area: 3 / 1 / auto / span1;
  }

  .footer__text.text-xs.is--legal {
    grid-row: 5;
    margin-top: 40px;
  }

  .footer__text.text-xs.is--email {
    grid-row: 1;
    width: -moz-fit-content;
    width: fit-content;
  }

  .footer__text.is--copyright.text-xs {
    grid-row: 3 / 3;
    margin-bottom: 0;
  }

  .footer__policy-links {
    grid-column-gap: 4px;
    grid-row-gap: 4px;
    grid-area: 2 / 1 / 2 / span1;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .section-gradient.is--ecosystem, .hero__animation {
    display: block;
  }

  .policy__rich p {
    font-size: 20px;
  }

  .policy__rich h2 {
    text-align: left;
  }

  .policy__rich h1 {
    text-align: left;
    font-size: 28px;
  }

  .footer__animation {
    top: 0%;
  }

  .hero__animation-canvas {
    flex: none;
    display: none !important;
  }

  .hero__mobile-video {
    object-fit: cover;
    flex: none;
    width: 100%;
    display: block;
  }

  .footer__animation-canvas {
    flex: none;
    display: none !important;
  }

  .footer__mobile-img {
    z-index: 0;
    aspect-ratio: auto;
    object-fit: cover;
    width: 100%;
    height: 500px;
    display: block;
    position: absolute;
    bottom: -32px;
    left: 0%;
    right: 0%;
  }

  .focus__circle-canvas {
    display: none !important;
  }

  .focus__circle-img {
    display: block;
  }

  .hero__desktop-video, .focus__circle-video, .footer__video {
    object-fit: cover;
    flex: none;
    width: 100%;
    display: none;
  }
}

@media screen and (max-width: 479px) {
  .footer__mobile-img {
    object-position: 50% 0%;
    width: 230vw;
    max-width: none;
    height: 660px;
    left: -10%;
  }
}

@media screen and (max-width: 991px) {
  #w-node-_5d9de279-3a53-a598-cc2c-81e1c30bd20b-c30bd202 {
    grid-area: span 1 / span 1 / span 1 / span 1;
  }
}


@font-face {
  font-family: 'SFT Schrifted Sans';
  src: url('../fonts/SFTSchriftedSans-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'SFT Schrifted Sans';
  src: url('../fonts/SFTSchriftedSans-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}