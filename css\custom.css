.submit-project {
  padding-top: 140px;
  padding-bottom: 140px;
  min-height: 525px;
}

@media screen and (max-width: 768px) {
  .submit-project {
    padding-top: 100px;
    padding-bottom: 100px;
  }
}

.submit-project__title {
  text-align: center;
  margin: 0 auto;
}

.submit-project__header {
  max-width: none;
  margin-bottom: 5rem;
}

@media screen and (max-width: 768px) {
  .submit-project__header {
    margin-bottom: 36px;
  }
}

.submit-project-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 624px;
  margin: 0 auto;
}

.form-input-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: relative;
  z-index: 10;
  width: 100%;
  padding-top: 24px;
  height: 5rem;
}

.form-input {
  width: 100%;
  padding-bottom: 0;
  border: none;
  height: 100%;
  border-bottom: 1px solid #000;
  background: none;
}

.form-input.invalid {
  border-bottom: 1px solid #ff363a;
}

.form-input--upload {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  padding-top: 8px;
  padding-bottom: 16px;
  padding-inline: 0;
  text-align: start;
}

.form-input-wrapper:has(textarea) {
  padding-top: 1.9rem;
  display: block;
  height: auto;
}

textarea.form-input {
  resize: vertical;
  height: auto;
  max-height: 200px;
  min-height: 100px;
}

.input-placeholder {
  position: absolute;
  top: 1.7rem;
  left: 0;
  color: rgba(0, 0, 0, 0.4);
  width: 100%;
  pointer-events: none;
  transform-origin: left top;
  transition-property: all;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
}

.form-input-error {
  margin-top: 4px;
  color: #ff363a;
}

.form-input__upload-description {
  color: rgba(0, 0, 0, 0.4);
  transform-origin: left top;
}

.file-input__button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  height: 48px;
  background: none;
  border: 1px solid #000;
  border-radius: 20px;
  cursor: pointer;
  transition-property: color, background-color;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
}

.file-input__button:hover {
  color: #fff;
  background-color: #000;
}

.file-input__preview-wrapper > * + * {
  margin-top: 8px;
}

.file-input__preview {
  display: flex;
  align-items: center;
  gap: 24px;
}

.file-input__preview-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.file-input__preview-remove {
  display: inline-flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: none;
  cursor: pointer;
  transition: opacity 0.3s ease-in-out;
  opacity: 0.24;
}

.file-input__preview-remove:is(:hover, :focus) {
  opacity: 0.8;
}

.form-input:is(:focus, :not(:placeholder-shown)) + .input-placeholder {
  transform: scale(0.8) translateY(-1.5rem);
}

.form-input__upload-description:has(
    + .file-input__preview-wrapper > .file-input__preview
  ) {
  transform: scale(0.8);
}

.form-checkboxes > * + * {
  margin-top: 12px;
}

.form-checkbox {
  display: flex;
  gap: 12px;
  align-items: center;
}

.form-checkbox__link {
  opacity: 0.4;
  transition: opacity 0.3s ease-in-out;
  text-underline-offset: 8px !important;
}

.form-checkbox__link::before {
  bottom: -2px !important;
}

.form-checkbox__link:hover {
  opacity: 1;
}

input[type="checkbox"] {
  display: grid;
  appearance: none;
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  border-radius: 0.15em;
  place-content: center;
  background-color: white;
  border: none !important;
  outline: 2px solid rgba(0, 0, 0, 0.12);

  transition: background-color 120ms ease-in-out;
}

input[type="checkbox"]:checked {
  background-color: rgba(0, 0, 0, 0.04);
  outline: 2px solid rgba(0, 0, 0, 0);
}

input[type="checkbox"]:focus {
  outline: 2px solid black !important;
}

input[type="checkbox"]::before {
  content: "";
  width: 20px;
  height: 20px;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em #000;
  clip-path: path(
    "M7.777 15.416 2.293 9.932l1.414-1.414 4.07 4.069 8.292-8.294 1.414 1.414-9.706 9.709Z"
  );
}

input[type="checkbox"].invalid {
  outline: 2px solid #FF363A !important;
}

input[type="checkbox"]:checked::before {
  transform: scale(1);
}

.form-success-message {
  display: none;
  max-width: 632px;
  text-align: center;
  margin: 0 auto;
}
